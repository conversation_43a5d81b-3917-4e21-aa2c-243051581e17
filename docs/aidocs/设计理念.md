
# 设计理念

## 1. 以业务组件为核心

### 1.1 业务组件的定义
业务组件是具有特定功能和UI的可复用单元，它封装了特定的业务逻辑和数据展示方式，可以被拖拽到画布上并进行配置。

### 1.2 业务组件的优势
- **提高开发效率**：通过预定义的组件，可以快速搭建页面，减少重复编码。
- **降低学习成本**：产品经理只需要熟悉组件的配置项，不需要深入了解前端技术。
- **保证一致性**：所有页面都基于相同的组件，保证了UI和交互的一致性。

### 1.3 技术特色
- **页面Json Schema**：只定义组件的内容，不定义样式，样式由组件实现定义。
- **API管理器**：统一管理所有API，包括请求、响应、数据映射等。
- **事件系统**：支持组件间的数据绑定和事件通信。
- **主题系统**：提供多套主题，支持自定义主题。

## 2. 以页面为单位

### 2.1 页面的定义
页面是业务组件的组合，它定义了页面的整体布局和组件的排列方式。

### 2.2 页面的优势
- **灵活性**：可以根据需求自由组合组件，实现不同的页面效果。
- **可维护性**：每个页面都是一个独立的单元，方便进行维护和升级。
- **可复用性**：可以将组件配置保存为模版在同一系统中的不同页面中复用。比如表格有哪些字段，避免数据表结构变更要在多处修改。

## 3. 面向产品经理

### 3.1 产品经理的角色
产品经理负责定义页面的需求和功能。

### 3.2 产品经理的需求
- **易用性**：提供直观的拖拽界面，方便产品经理进行页面设计。
- **可配置性**：提供丰富的组件配置项，满足不同需求。
- **可预览性**：提供实时预览功能，方便产品经理查看效果。

## 4. 专注泛监测产品体系
只解决数据安全类系统的低代码开发，这种系统属于后台管理类系统，组件复用度极高，页面的相似性也极高。
所以可以牺牲灵活性换取大幅降低系统的复杂度。

## 5. 轻量化Schema设计
### 5.1 Schema即配置
- **轻量DSL**：避免复杂的配置语法，只定义必要的业务配置
- **JSON格式**：易于理解、编辑和传输
- **版本管理**：支持Schema版本控制和向后兼容
- **差异化支持**：通过差异Schema解决客户定制需求

### 5.2 配置分离原则
- **内容与样式分离**：Schema只定义组件内容，样式由组件实现决定
- **数据与展示分离**：通过API配置统一管理数据源
- **逻辑与配置分离**：业务逻辑封装在组件内部，配置只暴露必要参数

## 6. 事件驱动架构
### 6.1 组件间通信
- **事件总线**：统一的事件通信机制
- **数据绑定**：支持组件间的数据依赖关系
- **级联更新**：支持表单字段的级联选择

### 6.2 API统一管理
- **集中配置**：所有API在Schema中统一定义
- **参数映射**：支持动态参数和响应数据映射
- **错误处理**：统一的API错误处理机制

## 7. 主题与定制化
### 7.1 多主题支持
- **行业主题**：针对不同行业提供专门的UI主题
- **品牌定制**：支持客户品牌色彩和Logo定制
- **响应式设计**：适配不同屏幕尺寸

### 7.2 组件扩展机制
- **插件化架构**：支持自定义业务组件注册
- **配置Schema**：每个组件都有对应的配置Schema
- **向后兼容**：新组件不影响现有Schema

## 8. 开发效率优先
### 8.1 快速迭代
- **所见即所得**：设计器提供实时预览功能
- **一键部署**：Schema + Runtime可直接部署
- **热更新**：支持配置的热更新，无需重新部署

### 8.2 学习成本最小化
- **拖拽式设计**：产品经理无需编码即可完成页面设计
- **配置向导**：提供字段配置、API配置的向导式界面
- **模板复用**：支持页面模板和组件配置模板

## 9. 整体架构

```
[设计器] → [Schema(JSON)] → [渲染器] → [后台页面]
     ↑                           ↓
 [业务组件库]                [组件实例]
```

* **设计器**：产品经理通过配置业务组件生成 schema。
* **Schema(JSON)**：轻量 DSL，基于业务组件的配置信息。
* **渲染器**：React runtime，根据 schema 渲染完整的后台页面。
* **业务组件库**：预定义的高级业务组件（导航、表格视图、状态栏等）。

## 10. 核心价值主张
### 10.1 对产品经理
- **零编码**：通过可视化配置完成页面设计
- **快速交付**：从需求到上线的时间大幅缩短
- **灵活调整**：页面调整无需开发介入

### 10.2 对开发团队
- **专注核心**：只需维护组件库和渲染器
- **标准化**：统一的开发规范和组件标准
- **可维护性**：清晰的架构分层，便于维护和扩展

### 10.3 对客户
- **快速定制**：通过差异Schema快速满足定制需求
- **成本可控**：减少重复开发，降低项目成本
- **稳定可靠**：基于成熟组件库，保证系统稳定性