import React, { useState } from 'react';
import { useDesigner } from '../../context/DesignerContext';
import { createDefaultComponentSchema } from '@lowcode/renderer';

export interface DesignComponentSidebarProps {
  className?: string;
  style?: React.CSSProperties;
}

// 组件数据定义
interface ComponentItem {
  key: string;
  label: string;
  icon: string;
}

interface TabData {
  key: string;
  label: string;
  icon: string;
  items: ComponentItem[];
}

const tabsData: TabData[] = [
  {
    key: 'layout',
    label: '布局',
    icon: 'view_quilt',
    items: [
      { key: 'Container', label: '容器', icon: 'view_quilt' }
    ]
  },
  {
    key: 'basics',
    label: '基础',
    icon: 'widgets',
    items: [
      { key: 'Text', label: '文本', icon: 'text_fields' },
      { key: 'Button', label: '按钮', icon: 'smart_button' },
      { key: 'Input', label: '输入框', icon: 'input' },
      { key: 'Image', label: '图片', icon: 'image' }
    ]
  },
  {
    key: 'business',
    label: '业务',
    icon: 'business_center',
    items: [
      { key: 'TableViewWithSearch', label: '表格', icon: 'table_chart' }
    ]
  }
];

export const DesignComponentSidebar: React.FC<DesignComponentSidebarProps> = ({
  className,
  style
}) => {
  const { addComponent } = useDesigner();
  const [activeTab, setActiveTab] = useState('basics');
  const [isCollapsed, setIsCollapsed] = useState(false);

  // 处理组件点击
  const handleComponentClick = (componentKey: string) => {
    const schema = createDefaultComponentSchema(componentKey);
    addComponent(schema);
  };

  // 渲染组件项
  const renderComponentItem = (item: ComponentItem) => (
    <div
      key={item.key}
      className="component-item"
      onClick={() => handleComponentClick(item.key)}
      style={{
        padding: '4px',
        border: '1px solid #e5e7eb',
        borderRadius: '6px',
        textAlign: 'center',
        cursor: 'move',
        transition: 'all 0.2s',
        backgroundColor: '#ffffff'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f9fafb';
        e.currentTarget.style.borderColor = '#3b82f6';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = '#ffffff';
        e.currentTarget.style.borderColor = '#e5e7eb';
      }}
    >
      <span
        className="material-icons"
        style={{
          fontSize: '24px',
          color: '#6b7280',
          display: 'block',
          marginBottom: '2px'
        }}
      >
        {item.icon}
      </span>
      <p style={{
        fontSize: '10px',
        color: '#1f2937',
        margin: 0,
        fontWeight: '500'
      }}>
        {item.label}
      </p>
    </div>
  );

  // 渲染基础组件分组
  const renderBasicsContent = () => {
    const basicsTab = tabsData.find(tab => tab.key === 'basics');
    if (!basicsTab) return null;

    return (
      <div style={{ padding: '6px' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr',
          gap: '4px'
        }}>
          {basicsTab.items.map(renderComponentItem)}
        </div>
      </div>
    );
  };

  // 渲染标签页内容
  const renderTabContent = () => {
    const currentTab = tabsData.find(tab => tab.key === activeTab);
    if (!currentTab) return null;

    if (activeTab === 'basics') {
      return renderBasicsContent();
    }

    return (
      <div style={{ padding: '6px' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr',
          gap: '4px'
        }}>
          {currentTab.items.map(renderComponentItem)}
        </div>
      </div>
    );
  };

  return (
    <aside
      className={className}
      style={{
        width: isCollapsed ? '0px' : '152px',
        backgroundColor: '#ffffff',
        borderRight: '1px solid #e5e7eb',
        display: 'flex',
        flexDirection: 'column',
        flexShrink: 0,
        transition: 'all 0.3s',
        position: 'relative',
        overflow: 'hidden',
        ...style
      }}
    >
      {/* 折叠按钮 */}
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        style={{
          position: 'absolute',
          right: '-12px',
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 10,
          backgroundColor: '#ffffff',
          border: '1px solid #d1d5db',
          borderRadius: '50%',
          padding: '2px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '24px',
          height: '24px'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = '#f3f4f6';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = '#ffffff';
        }}
      >
        <span 
          className="material-icons-outlined" 
          style={{ 
            fontSize: '16px', 
            color: '#6b7280',
            transition: 'transform 0.3s'
          }}
        >
          {isCollapsed ? 'chevron_right' : 'chevron_left'}
        </span>
      </button>

      {/* 侧边栏内容 */}
      <div 
        style={{
          opacity: isCollapsed ? 0 : 1,
          transition: 'opacity 0.3s',
          display: 'flex',
          flex: 1,
          overflow: 'hidden'
        }}
      >
        {/* 左侧标签栏 */}
        <div style={{
          width: '48px',
          borderRight: '1px solid #e5e7eb',
          flexShrink: 0
        }}>
          <nav style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '2px',
            padding: '4px'
          }}>
            {tabsData.map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  padding: '4px 2px',
                  borderRadius: '4px',
                  border: 'none',
                  borderLeft: '3px solid',
                  borderLeftColor: activeTab === tab.key ? '#3b82f6' : 'transparent',
                  backgroundColor: activeTab === tab.key ? '#eff6ff' : 'transparent',
                  color: activeTab === tab.key ? '#2563eb' : '#6b7280',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== tab.key) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                    e.currentTarget.style.color = '#374151';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== tab.key) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#6b7280';
                  }
                }}
              >
                <span className="material-icons" style={{ fontSize: '16px' }}>
                  {tab.icon}
                </span>
                <span style={{
                  fontSize: '10px',
                  fontWeight: '500',
                  marginTop: '2px'
                }}>
                  {tab.label}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* 右侧内容区 */}
        <div style={{
          flex: 1,
          overflowY: 'auto',
          transition: 'opacity 0.3s'
        }}>
          {renderTabContent()}
        </div>
      </div>
    </aside>
  );
};
