import React, { useMemo, useRef, useState } from 'react';
import { create<PERSON><PERSON><PERSON>, ComponentMeta, PropMeta, ComponentSchema, findComponent } from '@lowcode/renderer';
import { useDesigner } from '../../context/DesignerContext';

export interface PropertyPanelProps {
  width?: number;
  style?: React.CSSProperties;
  className?: string;
}

const Field: React.FC<{
  meta: PropMeta;
  value: any;
  onChange: (v: any) => void;
}> = ({ meta, value, onChange }) => {
  const commonStyle: React.CSSProperties = { width: '100%', boxSizing: 'border-box' };

  switch (meta.type) {
    case 'string':
      return (
        <input
          style={commonStyle}
          type="text"
          value={value ?? ''}
          onChange={(e) => onChange(e.target.value)}
        />
      );
    case 'number':
      return (
        <input
          style={commonStyle}
          type="number"
          value={value ?? ''}
          onChange={(e) => {
            const v = e.target.value;
            onChange(v === '' ? undefined : Number(v));
          }}
        />
      );
    case 'boolean':
      return (
        <label style={{ display: 'inline-flex', alignItems: 'center', gap: 8 }}>
          <input
            type="checkbox"
            checked={!!value}
            onChange={(e) => onChange(e.target.checked)}
          />
          <span>{value ? '是' : '否'}</span>
        </label>
      );
    case 'object':
    case 'array': {
      const [text, setText] = useState(() =>
        value !== undefined ? JSON.stringify(value, null, 2) : ''
      );
      const [error, setError] = useState<string | null>(null);
      return (
        <div>
          <textarea
            style={{ ...commonStyle, fontFamily: 'monospace', minHeight: 120 }}
            value={text}
            onChange={(e) => {
              const t = e.target.value;
              setText(t);
              if (t.trim() === '') {
                setError(null);
                onChange(undefined);
                return;
              }
              try {
                const json = JSON.parse(t);
                setError(null);
                onChange(json);
              } catch (err) {
                setError('JSON 格式错误');
              }
            }}
            placeholder={`请输入${meta.type === 'object' ? '对象' : '数组'}（JSON）`}
          />
          {error && (
            <div style={{ color: '#ff4d4f', fontSize: 12, marginTop: 4 }}>{error}</div>
          )}
        </div>
      );
    }
    case 'function':
    default:
      return (
        <input
          style={commonStyle}
          type="text"
          value={value ?? ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={`不支持的类型(${meta.type})，以字符串方式编辑`}
        />
      );
  }
};

export const PropertyPanel: React.FC<PropertyPanelProps> = ({ width = 320, style, className }) => {
  const { schema, canvasState, updateComponent } = useDesigner();
  const rendererRef = useRef(createRenderer());

  const selectedComponent: ComponentSchema | null = useMemo(() => {
    if (!canvasState.selectedComponentId) return null;
    return findComponent(schema.components, canvasState.selectedComponentId);
  }, [schema.components, canvasState.selectedComponentId]);

  const meta: ComponentMeta | undefined = useMemo(() => {
    if (!selectedComponent) return undefined;
    return rendererRef.current.getComponentRegistry().getMeta(selectedComponent.type);
  }, [selectedComponent]);

  const defaultStyle: React.CSSProperties = {
    width,
    height: '100%',
    backgroundColor: '#fafafa',
    borderLeft: '1px solid #e8e8e8',
    display: 'flex',
    flexDirection: 'column',
    ...style
  };

  const handlePropChange = (name: string, value: any) => {
    if (!selectedComponent) return;
    const nextProps = { ...(selectedComponent.props || {}) } as Record<string, any>;
    if (value === undefined) {
      delete nextProps[name];
    } else {
      nextProps[name] = value;
    }
    updateComponent(selectedComponent.id, { props: nextProps });
  };

  const getInitialValue = (p: PropMeta) => {
    const current = selectedComponent?.props?.[p.name];
    if (current !== undefined) return current;
    return p.default;
  };

  if (!selectedComponent) {
    return (
      <div className={`lowcode-property-panel ${className || ''}`} style={defaultStyle}>
        <div style={{ padding: 16, color: '#999' }}>请选择画布中的组件查看属性</div>
      </div>
    );
  }

  return (
    <div className={`lowcode-property-panel ${className || ''}`} style={defaultStyle}>
      {/* 标题 */}
      <div
        style={{
          padding: '16px',
          borderBottom: '1px solid #e8e8e8',
          backgroundColor: '#ffffff'
        }}
      >
        <div style={{ fontSize: 14, color: '#666' }}>已选组件</div>
        <div style={{ fontSize: 16, fontWeight: 600 }}>
          {meta?.name || selectedComponent.type}
          <span style={{ marginLeft: 8, color: '#999', fontSize: 12 }}>#{selectedComponent.id}</span>
        </div>
      </div>

      {/* 属性编辑区 */}
      <div style={{ flex: 1, overflow: 'auto', padding: 16 }}>
        {(meta?.props || []).map((p) => (
          <div key={p.name} style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 6 }}>
              <label style={{ fontSize: 13, fontWeight: 500 }}>{p.name}</label>
              {p.required && <span style={{ color: '#ff4d4f', fontSize: 12 }}>*必填</span>}
            </div>
            {p.description && (
              <div style={{ color: '#999', fontSize: 12, marginBottom: 6 }}>{p.description}</div>
            )}
            <Field meta={p} value={getInitialValue(p)} onChange={(v) => handlePropChange(p.name, v)} />
          </div>
        ))}

        {/* 兜底：style 与 className 快捷编辑 */}
        <div style={{ marginTop: 24, paddingTop: 12, borderTop: '1px dashed #e8e8e8' }}>
          <div style={{ fontSize: 13, fontWeight: 600, marginBottom: 8 }}>通用</div>
          <div style={{ marginBottom: 12 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 6 }}>
              <label style={{ fontSize: 13, fontWeight: 500 }}>className</label>
            </div>
            <input
              type="text"
              style={{ width: '100%' }}
              value={selectedComponent.className || ''}
              onChange={(e) => updateComponent(selectedComponent.id, { className: e.target.value })}
            />
          </div>
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 6 }}>
              <label style={{ fontSize: 13, fontWeight: 500 }}>style (JSON)</label>
            </div>
            <textarea
              style={{ width: '100%', minHeight: 120, fontFamily: 'monospace' }}
              value={JSON.stringify(selectedComponent.style || {}, null, 2)}
              onChange={(e) => {
                const val = e.target.value;
                try {
                  const obj = val ? JSON.parse(val) : {};
                  updateComponent(selectedComponent.id, { style: obj });
                } catch (err) {
                  // ignore parse error while typing
                }
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

