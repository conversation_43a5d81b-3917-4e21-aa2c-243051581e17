import React from 'react';
import { useDesigner } from '../../context/DesignerContext';

export interface ToolbarProps {
  height?: number;
  style?: React.CSSProperties;
  className?: string;
}

export const Toolbar: React.FC<ToolbarProps> = ({ height = 48, style, className }) => {
  const { schema } = useDesigner();

  const defaultStyle: React.CSSProperties = {
    height,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 24px',
    background: '#fff',
    borderBottom: '1px solid #e8e8e8',
    ...style
  };

  const openPreview = () => {
    // 将schema数据存储到sessionStorage
    sessionStorage.setItem('lowcode-preview-schema', JSON.stringify(schema));

    // 打开新窗口到预览页面
    const previewUrl = `${window.location.origin}${window.location.pathname}?mode=preview`;
    window.open(previewUrl, '_blank', 'width=1200,height=800');
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '20px',
    fontWeight: '700',
    color: '#1f2937',
    margin: 0
  };

  const buttonStyle: React.CSSProperties = {
    backgroundColor: '#2563eb',
    color: '#ffffff',
    fontWeight: '600',
    padding: '8px 16px',
    borderRadius: '6px',
    border: 'none',
    fontSize: '14px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  };

  const buttonHoverStyle: React.CSSProperties = {
    backgroundColor: '#1d4ed8'
  };

  return (
    <div className={`lowcode-toolbar ${className || ''}`} style={defaultStyle}>
      {/* 左侧：页面标题 */}
      <h1 style={titleStyle}>
        {schema.title || '低代码平台示例'}
      </h1>

      {/* 右侧：预览按钮 */}
      <button
        style={buttonStyle}
        onMouseEnter={(e) => Object.assign(e.currentTarget.style, buttonHoverStyle)}
        onMouseLeave={(e) => Object.assign(e.currentTarget.style, buttonStyle)}
        onClick={openPreview}
      >
        预览
      </button>
    </div>
  );
};

