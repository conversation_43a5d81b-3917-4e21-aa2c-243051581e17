import React from 'react';
import { tokens, focusRing } from '../../theme/tokens';

export type LibraryTab = 'layout' | 'basics' | 'business';

export interface LibraryItem {
  key: string;
  icon?: React.ReactNode;
  label: string;
}

export interface ComponentLibrarySidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  collapsed?: boolean;
  defaultTab?: LibraryTab;
  layoutItems?: LibraryItem[];
  basicItems?: LibraryItem[];
  businessItems?: LibraryItem[];
  onToggleCollapse?: (collapsed: boolean) => void;
  onTabChange?: (tab: LibraryTab) => void;
  onItemClick?: (tab: LibraryTab, item: LibraryItem) => void;
  width?: number; // 展开宽度
}

export const ComponentLibrarySidebar: React.FC<ComponentLibrarySidebarProps> = ({
  collapsed: collapsedProp,
  defaultTab = 'basics',
  layoutItems = [
    { key: 'container', label: '容器', icon: 'view_quilt' },
  ],
  basicItems = [
    { key: 'text', label: '文本', icon: 'text_fields' },
    { key: 'button', label: '按钮', icon: 'smart_button' },
    { key: 'input', label: '输入框', icon: 'input' },
    { key: 'image', label: '图片', icon: 'image' },
  ],
  businessItems = [
    { key: 'table', label: '表格', icon: 'table_chart' },
  ],
  onToggleCollapse,
  onTabChange,
  onItemClick,
  width = 256,
  style,
  className,
  ...rest
}) => {
  const [collapsed, setCollapsed] = React.useState<boolean>(!!collapsedProp);
  const [tab, setTab] = React.useState<LibraryTab>(defaultTab);
  const [basicOpen, setBasicOpen] = React.useState<boolean>(true);

  React.useEffect(() => {
    if (collapsedProp !== undefined) setCollapsed(collapsedProp);
  }, [collapsedProp]);

  const handleToggle = () => {
    const next = !collapsed;
    setCollapsed(next);
    onToggleCollapse?.(next);
  };

  const tabButton = (current: LibraryTab, label: string, icon?: React.ReactNode) => {
    const active = tab === current;
    return (
      <button
        onClick={() => { setTab(current); onTabChange?.(current); }}
        className={`lc-tab ${active ? 'lc-tab-active' : 'lc-tab-inactive'}`}
      >
        <span className="material-icons" style={{ fontSize: 20, lineHeight: '20px' }}>{icon as any}</span>
        <span style={{ fontSize: 12, marginTop: 4, fontWeight: 500 }}>{label}</span>
      </button>
    );
  };

  const card = (item: LibraryItem, currentTab: LibraryTab) => (
    <div
      key={item.key}
      onClick={() => onItemClick?.(currentTab, item)}
      className="lc-card"
    >
      <span className="material-icons lc-card-icon">{item.icon as any}</span>
      <p className="lc-card-title">{item.label}</p>
    </div>
  );

  const panel = (items: LibraryItem[], currentTab: LibraryTab) => (
    <div className="lc-grid">
      {items.map(i => card(i, currentTab))}
    </div>
  );

  const sidebarWidth = collapsed ? 0 : width;

  return (
    <aside
      className={`lc-component-library ${className || ''}`}
      style={{
        width: sidebarWidth,
        transition: 'width .3s ease',
        ...style,
      }}
      {...rest}
    >
      {/* 折叠按钮 */}
      <button
        onClick={handleToggle}
        aria-label="toggle"
        className="lc-toggle-button"
      >
        <span className="material-icons-outlined" style={{ color: '#4b5563', transition: 'transform .2s' }}>
          {collapsed ? 'chevron_right' : 'chevron_left'}
        </span>
      </button>

      <div className="lc-content" style={{ opacity: collapsed ? 0 : 1 }}>
        {/* 左侧标签栏 */}
        <div className="lc-sidebar-nav">
          <nav className="lc-nav-inner">
            {tabButton('layout', '布局', 'view_quilt')}
            {tabButton('basics', '基础', 'widgets')}
            {tabButton('business', '业务', 'business_center')}
          </nav>
        </div>

        {/* 右侧面板区 */}
        <div className="lc-panel">
          {tab === 'layout' && panel(layoutItems, 'layout')}
          {tab === 'basics' && (
            <div style={{ padding: '0 8px' }}>
              <button className="lc-section-header" onClick={() => setBasicOpen(!basicOpen)}>
                <span className="lc-section-title">基础组件</span>
                <span className="material-icons lc-icon-transition" style={{ transform: basicOpen ? 'rotate(180deg)' : 'rotate(0deg)' }}>expand_more</span>
              </button>
              <div className={`lc-collapse ${basicOpen ? 'lc-open' : 'lc-closed'}`}>
                <div className="lc-grid">
                  {basicItems.map(i => card(i, 'basics'))}
                </div>
              </div>
            </div>
          )}
          {tab === 'business' && panel(businessItems, 'business')}
        </div>
      </div>
    </aside>
  );
};

