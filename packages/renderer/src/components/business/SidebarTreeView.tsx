import React from 'react';

export interface TreeNode {
  key: string;
  title: string;
  icon?: string;
  href?: string;
  children?: TreeNode[];
  disabled?: boolean;
  count?: number; // 添加计数显示
}

export interface SidebarTreeViewProps extends React.HTMLAttributes<HTMLDivElement> {
  data: TreeNode[];
  width?: number | string;
  height?: number | string;
  searchable?: boolean;
  searchPlaceholder?: string;
  defaultExpandedKeys?: string[];
  defaultSelectedKeys?: string[];
  showIcon?: boolean;
  style?: React.CSSProperties;
  className?: string;
  onSelect?: (selectedKeys: string[], node: TreeNode) => void;
  onExpand?: (expandedKeys: string[], node: TreeNode) => void;
  onSearch?: (value: string) => void;
}

export const SidebarTreeView: React.FC<SidebarTreeViewProps> = ({
  data,
  width = 240,
  height = '100%',
  searchable = true,
  searchPlaceholder = '搜索...',
  defaultExpandedKeys = [],
  defaultSelectedKeys = [],
  showIcon = true,
  style,
  className,
  onSelect,
  onExpand,
  onSearch,
  ...rest
}) => {
  const [expandedKeys, setExpandedKeys] = React.useState<string[]>(defaultExpandedKeys);
  const [selectedKeys, setSelectedKeys] = React.useState<string[]>(defaultSelectedKeys.length > 0 ? defaultSelectedKeys : ['all']);
  const [searchValue, setSearchValue] = React.useState<string>('');
  const [filteredData, setFilteredData] = React.useState<TreeNode[]>(data);

  // 搜索功能
  React.useEffect(() => {
    if (!searchValue) {
      setFilteredData(data);
      return;
    }

    const filterTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.reduce((acc: TreeNode[], node) => {
        const matchesSearch = node.title.toLowerCase().includes(searchValue.toLowerCase());
        const filteredChildren = node.children ? filterTree(node.children) : [];

        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren.length > 0 ? filteredChildren : node.children
          });
        }

        return acc;
      }, []);
    };

    const filtered = filterTree(data);
    setFilteredData(filtered);

    // 搜索时自动展开所有匹配的节点
    if (searchValue) {
      const getAllKeys = (nodes: TreeNode[]): string[] => {
        let keys: string[] = [];
        nodes.forEach(node => {
          keys.push(node.key);
          if (node.children) {
            keys = keys.concat(getAllKeys(node.children));
          }
        });
        return keys;
      };
      setExpandedKeys(getAllKeys(filtered));
    }
  }, [searchValue, data]);

  const handleNodeClick = (node: TreeNode) => {
    if (node.disabled) return;

    // 处理选中状态
    const newSelectedKeys = [node.key];
    setSelectedKeys(newSelectedKeys);
    onSelect?.(newSelectedKeys, node);

    // 如果有链接，进行跳转
    if (node.href) {
      window.location.href = node.href;
    }
  };

  const handleNodeExpand = (node: TreeNode) => {
    const isExpanded = expandedKeys.includes(node.key);
    let newExpandedKeys: string[];

    if (isExpanded) {
      newExpandedKeys = expandedKeys.filter(key => key !== node.key);
    } else {
      newExpandedKeys = [...expandedKeys, node.key];
    }

    setExpandedKeys(newExpandedKeys);
    onExpand?.(newExpandedKeys, node);
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    onSearch?.(value);
  };

  const renderTreeNode = (node: TreeNode, level: number = 0): React.ReactElement => {
    const isExpanded = expandedKeys.includes(node.key);
    const isSelected = selectedKeys.includes(node.key);
    const hasChildren = node.children && node.children.length > 0;
    const paddingLeft = level * 16 + 16;

    return (
      <div key={node.key}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '8px 16px',
            paddingLeft: `${paddingLeft}px`,
            cursor: node.disabled ? 'not-allowed' : 'pointer',
            backgroundColor: isSelected ? '#eff6ff' : 'transparent',
            color: node.disabled ? '#9ca3af' : isSelected ? '#3b82f6' : '#374151',
            transition: 'all 0.2s',
            fontSize: '14px',
            userSelect: 'none',
            minHeight: '40px'
          }}
          onClick={() => handleNodeClick(node)}
          onMouseEnter={(e) => {
            if (!node.disabled && !isSelected) {
              e.currentTarget.style.backgroundColor = '#f9fafb';
            }
          }}
          onMouseLeave={(e) => {
            if (!node.disabled && !isSelected) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          {/* 左侧内容 */}
          <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            {/* 展开/收起图标 */}
            {hasChildren ? (
              <span
                className="material-icons"
                style={{
                  marginRight: '8px',
                  fontSize: '16px',
                  transition: 'transform 0.2s',
                  transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                  cursor: 'pointer',
                  color: '#6b7280'
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleNodeExpand(node);
                }}
              >
                chevron_right
              </span>
            ) : (
              <span style={{ marginRight: '24px' }} />
            )}

            {/* 节点图标 */}
            {showIcon && node.icon && (
              <span style={{ marginRight: '8px', fontSize: '16px' }}>
                {node.icon}
              </span>
            )}

            {/* 节点标题 */}
            <span style={{
              fontWeight: isSelected ? '500' : '400',
              color: isSelected ? '#3b82f6' : '#374151'
            }}>
              {node.title}
            </span>

            {/* 信息图标（如果是选中的全部API项） */}
            {isSelected && node.key === 'all' && (
              <span
                className="material-icons"
                style={{
                  marginLeft: '6px',
                  fontSize: '16px',
                  color: '#3b82f6'
                }}
              >
                info
              </span>
            )}
          </div>

          {/* 右侧计数 */}
          {typeof node.count === 'number' && (
            <span style={{
              fontSize: '14px',
              color: isSelected ? '#3b82f6' : '#9ca3af',
              fontWeight: isSelected ? '500' : '400',
              marginLeft: '8px'
            }}>
              {node.count}
            </span>
          )}
        </div>

        {/* 子节点 */}
        {hasChildren && isExpanded && (
          <div>
            {node.children!.map(child => renderTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const defaultStyle: React.CSSProperties = {
    width,
    height,
    backgroundColor: '#ffffff',
    borderRight: '1px solid #e5e7eb',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
    ...style,
  };

  return (
    <div className={`lowcode-sidebar-tree-view ${className || ''}`} style={defaultStyle} {...rest}>
      {/* 搜索框 */}
      {searchable && (
        <div style={{ padding: '16px 16px 12px 16px' }}>
          <div style={{ position: 'relative' }}>
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => handleSearch(e.target.value)}
              style={{
                width: '100%',
                height: '36px',
                padding: '8px 40px 8px 12px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                background: '#ffffff',
                transition: 'all 0.2s',
                color: '#374151'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59,130,246,0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.boxShadow = 'none';
              }}
            />
            {/* 搜索图标 */}
            <span
              className="material-icons"
              style={{
                position: 'absolute',
                right: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '20px',
                color: '#9ca3af',
                pointerEvents: 'none'
              }}
            >
              search
            </span>
          </div>
        </div>
      )}

      {/* 树形结构 */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        {filteredData.length > 0 ? (
          filteredData.map(node => renderTreeNode(node))
        ) : (
          <div
            style={{
              padding: '20px',
              textAlign: 'center',
              color: '#bfbfbf',
              fontSize: '14px',
            }}
          >
            {searchValue ? '没有找到匹配的结果' : '暂无数据'}
          </div>
        )}
      </div>
    </div>
  );
};
