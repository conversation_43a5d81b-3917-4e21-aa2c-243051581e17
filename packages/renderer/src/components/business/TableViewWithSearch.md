# TableViewWithSearch 组件

## 概述

TableViewWithSearch 是一个功能完整的表格组件，支持搜索、分页、排序、行选择等功能。该组件已根据设计图进行了像素级还原，提供了现代化的UI设计。

## 新增功能

### 1. 页面标题
- 支持显示页面标题
- 标题右侧可以放置工具栏按钮

### 2. 标签页功能
- 支持多个标签页切换
- 每个标签页可以显示数量徽章
- 活跃标签页有视觉高亮效果

### 3. 更多操作区域
- 显示已选择项目数量
- 批量操作按钮
- 更多条件筛选按钮
- 确定按钮

### 4. 优化的搜索表单
- 4列网格布局
- 现代化的输入框和下拉框样式
- 更好的焦点状态和交互效果

### 5. 改进的表格样式
- 更清晰的表头设计
- 优化的行间距和内边距
- 改进的排序图标
- 更好的悬停效果

### 6. 增强的分页组件
- 更现代的分页控件样式
- 优化的页面信息显示
- 改进的页面跳转功能

## 使用示例

```tsx
import { TableViewWithSearch } from './TableViewWithSearch';

const Example = () => {
  const columns = [
    {
      key: 'name',
      title: '名称',
      dataIndex: 'name',
      width: '200px'
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      sortable: true
    }
  ];

  const searchFields = [
    {
      key: 'name',
      label: '名称',
      type: 'input',
      placeholder: '请输入名称'
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'inactive' }
      ]
    }
  ];

  const tabs = [
    { key: 'all', label: '全部', count: 100 },
    { key: 'active', label: '启用', count: 80 },
    { key: 'inactive', label: '禁用', count: 20 }
  ];

  const toolbarActions = [
    {
      key: 'batch',
      label: '批量操作',
      type: 'default'
    },
    {
      key: 'create',
      label: '新建',
      type: 'primary'
    }
  ];

  return (
    <TableViewWithSearch
      title="数据管理"
      columns={columns}
      dataSource={data}
      searchFields={searchFields}
      tabs={tabs}
      activeTab="all"
      toolbarActions={toolbarActions}
      pagination={{
        current: 1,
        pageSize: 25,
        total: 100,
        showSizeChanger: true,
        showQuickJumper: true
      }}
      rowSelection={{
        type: 'checkbox',
        selectedRowKeys: [],
        onChange: (keys, rows) => {
          console.log('选择行:', keys, rows);
        }
      }}
      onSearch={(values) => {
        console.log('搜索:', values);
      }}
      onTabChange={(key) => {
        console.log('切换标签:', key);
      }}
      onTableChange={(pagination, filters, sorter) => {
        console.log('表格变化:', { pagination, filters, sorter });
      }}
    />
  );
};
```

## 新增属性

### TableViewWithSearchProps

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | string | - | 页面标题 |
| tabs | TabItem[] | [] | 标签页配置 |
| activeTab | string | - | 当前活跃的标签页 |
| onTabChange | (key: string) => void | - | 标签页切换回调 |

### TabItem

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| key | string | - | 标签页唯一标识 |
| label | string | - | 标签页显示文本 |
| count | number | - | 标签页数量徽章（可选） |

## 设计特色

1. **现代化UI**: 采用现代化的设计语言，圆角、阴影、间距等都经过精心设计
2. **响应式布局**: 搜索表单采用网格布局，自适应不同屏幕尺寸
3. **交互友好**: 丰富的悬停效果、焦点状态和过渡动画
4. **信息层次**: 清晰的信息层次结构，重要信息突出显示
5. **一致性**: 所有组件保持一致的设计风格和交互模式

## 技术实现

- 使用纯CSS-in-JS实现样式，无外部依赖
- 支持TypeScript，提供完整的类型定义
- 遵循React最佳实践，使用函数组件和Hooks
- 性能优化，避免不必要的重渲染
- 可访问性友好，支持键盘导航和屏幕阅读器
