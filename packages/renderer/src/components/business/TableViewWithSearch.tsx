import React from 'react';
import { tokens, focusRing } from '../../theme/tokens';

export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

export interface TableAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: (record: any, index: number) => void;
}

export interface ToolbarAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: () => void;
}

export interface SearchField {
  key: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'dateRange';
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
}

export interface TableViewWithSearchProps extends React.HTMLAttributes<HTMLDivElement> {
  columns: TableColumn[];
  dataSource: any[];
  loading?: boolean;
  searchFields?: SearchField[];
  toolbarActions?: ToolbarAction[];
  rowActions?: TableAction[];
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showTotal?: boolean;
  };
  rowSelection?: {
    type: 'checkbox' | 'radio';
    selectedRowKeys?: string[];
    onChange?: (selectedRowKeys: string[], selectedRows: any[]) => void;
  };
  style?: React.CSSProperties;
  className?: string;
  onSearch?: (searchValues: Record<string, any>) => void;
  onTableChange?: (pagination: any, filters: any, sorter: any) => void;
  onRow?: (record: any, index: number) => React.HTMLAttributes<HTMLTableRowElement>;
}

export const TableViewWithSearch: React.FC<TableViewWithSearchProps> = ({
  columns,
  dataSource,
  loading = false,
  searchFields = [],
  toolbarActions = [],
  rowActions = [],
  pagination,
  rowSelection,
  style,
  className,
  onSearch,
  onTableChange,
  onRow,
  ...rest
}) => {
  const [searchValues, setSearchValues] = React.useState<Record<string, any>>({});
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>(
    rowSelection?.selectedRowKeys || []
  );
  const [sortConfig, setSortConfig] = React.useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // 处理搜索
  const handleSearch = () => {
    onSearch?.(searchValues);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValues({});
    onSearch?.({});
  };

  // 处理搜索字段变化
  const handleSearchFieldChange = (key: string, value: any) => {
    const newValues = { ...searchValues, [key]: value };
    setSearchValues(newValues);
  };

  // 处理行选择
  const handleRowSelection = (keys: string[], rows: any[]) => {
    setSelectedRowKeys(keys);
    rowSelection?.onChange?.(keys, rows);
  };

  // 处理排序
  const handleSort = (column: TableColumn) => {
    if (!column.sortable) return;

    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig?.key === column.key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }

    setSortConfig({ key: column.key, direction });
    onTableChange?.(pagination, {}, { field: column.key, order: direction });
  };

  // 渲染搜索表单
  const renderSearchForm = () => {
    if (searchFields.length === 0) return null;

    return (
      <div style={{
        padding: '12px 16px',
        backgroundColor: tokens.color.bg,
        border: `1px solid ${tokens.color.border}`,
        borderRadius: `${tokens.radius.lg}px`,
        marginBottom: '12px',
        boxShadow: tokens.shadow.xs
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(220px, 1fr))',
          gap: '10px 16px',
          marginBottom: '8px'
        }}>
          {searchFields.map(field => (
            <div key={field.key}>
              <label style={{
                display: 'block',
                marginBottom: '4px',
                fontSize: '12px',
                color: '#666'
              }}>
                {field.label}
              </label>
              {field.type === 'input' && (
                <input
                  type="text"
                  placeholder={field.placeholder}
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '32px',
                    padding: '4px 10px',
                    border: `1px solid ${tokens.color.border}`,
                    borderRadius: `${tokens.radius.md}px`,
                    fontSize: '12px',
                    outline: 'none'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = tokens.color.brand;
                    e.currentTarget.style.boxShadow = focusRing();
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = tokens.color.border;
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              )}
              {field.type === 'select' && (
                <select
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '32px',
                    padding: '4px 10px',
                    border: `1px solid ${tokens.color.border}`,
                    borderRadius: `${tokens.radius.md}px`,
                    fontSize: '12px',
                    outline: 'none'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = tokens.color.brand;
                    e.currentTarget.style.boxShadow = focusRing();
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = tokens.color.border;
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  <option value="">{field.placeholder || '请选择'}</option>
                  {field.options?.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              )}
              {field.type === 'date' && (
                <input
                  type="date"
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '28px',
                    padding: '2px 8px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}
                />
              )}
            </div>
          ))}
        </div>

        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={handleSearch}
            style={{
              height: '28px',
              padding: '0 10px',
              backgroundColor: '#1677ff',
              color: '#ffffff',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            搜索
          </button>
          <button
            onClick={handleReset}
            style={{
              height: '28px',
              padding: '0 10px',
              backgroundColor: '#ffffff',
              color: '#374151',
              border: '1px solid #e5e7eb',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            重置
          </button>
        </div>
      </div>
    );
  };

  // 渲染工具栏
  const renderToolbar = () => {
    if (toolbarActions.length === 0) return null;

    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '10px 12px',
        border: `1px solid ${tokens.color.border}`,
        borderRadius: `${tokens.radius.lg}px`,
        backgroundColor: tokens.color.bg,
        marginBottom: '12px',
        boxShadow: tokens.shadow.xs
      }}>
        <div style={{ display: 'flex', gap: '8px' }}>
          {toolbarActions.map(action => (
            <button
              key={action.key}
              onClick={action.onClick}
              disabled={action.disabled}
              style={{
                height: '32px',
                padding: '0 12px',
                backgroundColor: action.type === 'primary' ? '#3b82f6' : action.type === 'danger' ? '#ef4444' : '#ffffff',
                color: action.type === 'primary' || action.type === 'danger' ? '#ffffff' : '#374151',
                border: action.type === 'default' ? '1px solid #e5e7eb' : '1px solid transparent',
                borderRadius: '8px',
                cursor: action.disabled ? 'not-allowed' : 'pointer',
                fontSize: '12px',
                opacity: action.disabled ? 0.6 : 1,
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e)=>{ e.currentTarget.style.backgroundColor = action.type==='primary' ? '#2563eb' : action.type==='danger' ? '#dc2626' : '#f3f4f6'; }}
              onMouseLeave={(e)=>{ e.currentTarget.style.backgroundColor = action.type==='primary' ? '#3b82f6' : action.type==='danger' ? '#ef4444' : '#ffffff'; }}
            >
              {action.icon && <span style={{ marginRight: '6px' }}>{action.icon}</span>}
              {action.label}
            </button>
          ))}
        </div>

        {rowSelection && selectedRowKeys.length > 0 && (
          <div style={{ fontSize: '12px', color: '#6b7280' }}>
            已选择 {selectedRowKeys.length} 项
          </div>
        )}
      </div>
    );
  };

  // 渲染表格
  const renderTable = () => {
    return (
      <div style={{
        border: `1px solid ${tokens.color.border}`,
        borderRadius: `${tokens.radius.lg}px`,
        overflow: 'hidden',
        backgroundColor: tokens.color.bg,
        boxShadow: tokens.shadow.xs
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f9fafb' }}>
              {rowSelection && (
                <th style={{
                  padding: '8px 12px',
                  textAlign: 'left',
                  borderBottom: '1px solid #eef2f7',
                  width: '48px'
                }}>
                  <input
                    type={rowSelection.type === 'radio' ? 'radio' : 'checkbox'}
                    checked={selectedRowKeys.length === dataSource.length && dataSource.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        const allKeys = dataSource.map((_, index) => index.toString());
                        handleRowSelection(allKeys, dataSource);
                      } else {
                        handleRowSelection([], []);
                      }
                    }}
                  />
                </th>
              )}
              {columns.map(column => (
                <th
                  key={column.key}
                  style={{
                    padding: '8px 12px',
                    textAlign: column.align || 'left',
                    borderBottom: '1px solid #eef2f7',
                    width: column.width,
                    cursor: column.sortable ? 'pointer' : 'default',
                    userSelect: 'none',
                    whiteSpace: 'nowrap'
                  }}
                  onClick={() => handleSort(column)}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <span>{column.title}</span>
                    {column.sortable && (
                      <span style={{ fontSize: '12px' }}>
                        {sortConfig?.key === column.key ? (
                          sortConfig.direction === 'asc' ? '↑' : '↓'
                        ) : '↕'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              {rowActions.length > 0 && (
                <th style={{
                  padding: '8px 12px',
                  textAlign: 'center',
                  borderBottom: '1px solid #eef2f7'
                }}>
                  操作
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                  style={{
                    padding: '32px',
                    textAlign: 'center',
                    color: '#666',
                    backgroundColor: '#fff'
                  }}
                >
                  加载中...
                </td>
              </tr>
            ) : dataSource.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                  style={{
                    padding: '32px',
                    textAlign: 'center',
                    color: '#666',
                    backgroundColor: '#fff'
                  }}
                >
                  暂无数据
                </td>
              </tr>
            ) : (
              dataSource.map((record, index) => {
                const rowKey = index.toString();
                const isSelected = selectedRowKeys.includes(rowKey);
                const rowProps = onRow?.(record, index) || {};

                return (
                  <tr
                    key={rowKey}
                    {...rowProps}
                    style={{
                      backgroundColor: isSelected ? '#eef5ff' : 'transparent',
                      ...rowProps.style
                    }}
                    onMouseEnter={(e) => {
                      if (!isSelected) {
                        e.currentTarget.style.backgroundColor = '#f8fafc';
                      }
                      rowProps.onMouseEnter?.(e);
                    }}
                    onMouseLeave={(e) => {
                      if (!isSelected) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                      rowProps.onMouseLeave?.(e);
                    }}
                  >
                    {rowSelection && (
                      <td style={{
                        padding: '10px 12px',
                        borderBottom: '1px solid #eef2f7'
                      }}>
                        <input
                          type={rowSelection.type === 'radio' ? 'radio' : 'checkbox'}
                          checked={isSelected}
                          onChange={(e) => {
                            if (rowSelection.type === 'radio') {
                              handleRowSelection([rowKey], [record]);
                            } else {
                              if (e.target.checked) {
                                handleRowSelection([...selectedRowKeys, rowKey], [...selectedRowKeys.map(key => dataSource[parseInt(key)]), record]);
                              } else {
                                const newKeys = selectedRowKeys.filter(key => key !== rowKey);
                                handleRowSelection(newKeys, newKeys.map(key => dataSource[parseInt(key)]));
                              }
                            }
                          }}
                        />
                      </td>
                    )}
                    {columns.map(column => {
                      const value = record[column.dataIndex];
                      const cellContent = column.render ? column.render(value, record, index) : value;

                      return (
                        <td
                          key={column.key}
                          style={{
                            padding: '10px 12px',
                            textAlign: column.align || 'left',
                            borderBottom: '1px solid #eef2f7'
                          }}
                        >
                          {cellContent}
                        </td>
                      );
                    })}
                    {rowActions.length > 0 && (
                      <td style={{
                        padding: '10px 12px',
                        textAlign: 'center',
                        borderBottom: '1px solid #eef2f7'
                      }}>
                        <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                          {rowActions.map(action => (
                            <button
                              key={action.key}
                              onClick={() => action.onClick?.(record, index)}
                              disabled={action.disabled}
                              style={{
                                height: '24px',
                                padding: '0 6px',
                                backgroundColor: action.type === 'primary' ? '#1677ff' :
                                                action.type === 'danger' ? '#ef4444' : 'transparent',
                                color: action.type === 'primary' || action.type === 'danger' ? '#ffffff' : '#1677ff',
                                border: action.type === 'primary' || action.type === 'danger' ? '1px solid transparent' : 'none',
                                borderRadius: '4px',
                                cursor: action.disabled ? 'not-allowed' : 'pointer',
                                fontSize: '12px',
                                opacity: action.disabled ? 0.6 : 1
                              }}
                            >
                              {action.icon && <span style={{ marginRight: '4px' }}>{action.icon}</span>}
                              {action.label}
                            </button>
                          ))}
                        </div>
                      </td>
                    )}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    );
  };

  // 渲染分页
  const renderPagination = () => {
    if (!pagination) return null;

    const { current, pageSize, total, showSizeChanger, showQuickJumper, showTotal } = pagination;
    const totalPages = Math.ceil(total / pageSize);

    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: '12px',
        padding: '12px 0'
      }}>
        {showTotal && (
          <div style={{ fontSize: '12px', color: '#666' }}>
            共 {total} 条记录
          </div>
        )}

        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {showSizeChanger && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontSize: '12px' }}>每页显示</span>
              <select
                value={pageSize}
                onChange={(e) => {
                  const newPageSize = parseInt(e.target.value);
                  onTableChange?.({ ...pagination, pageSize: newPageSize, current: 1 }, {}, {});
                }}
                style={{
                  height: '28px',
                  padding: '0 8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}
              >
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span style={{ fontSize: '14px' }}>条</span>
            </div>
          )}

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <button
              disabled={current <= 1}
              onClick={() => onTableChange?.({ ...pagination, current: current - 1 }, {}, {})}
              style={{
                height: '28px',
                padding: '0 8px',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                backgroundColor: '#ffffff',
                cursor: current <= 1 ? 'not-allowed' : 'pointer',
                opacity: current <= 1 ? 0.6 : 1
              }}
            >
              上一页
            </button>

            {showQuickJumper && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '12px' }}>跳至</span>
                <input
                  type="number"
                  min={1}
                  max={totalPages}
                  style={{
                    width: '56px',
                    height: '28px',
                    padding: '0 8px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      const page = parseInt((e.target as HTMLInputElement).value);
                      if (page >= 1 && page <= totalPages) {
                        onTableChange?.({ ...pagination, current: page }, {}, {});
                      }
                    }
                  }}
                />
                <span style={{ fontSize: '14px' }}>页</span>
              </div>
            )}

            <span style={{ fontSize: '12px' }}>
              {current} / {totalPages}
            </span>

            <button
              disabled={current >= totalPages}
              onClick={() => onTableChange?.({ ...pagination, current: current + 1 }, {}, {})}
              style={{
                height: '28px',
                padding: '0 8px',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                backgroundColor: '#ffffff',
                cursor: current >= totalPages ? 'not-allowed' : 'pointer',
                opacity: current >= totalPages ? 0.6 : 1
              }}
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    );
  };

  const defaultStyle: React.CSSProperties = {
    backgroundColor: '#ffffff',
    borderRadius: 0,
    padding: '12px',
    ...style,
  };

  return (
    <div className={`lowcode-table-view-with-search ${className || ''}`} style={defaultStyle} {...rest}>
      {renderSearchForm()}
      {renderToolbar()}
      {renderTable()}
      {renderPagination()}
    </div>
  );
};
