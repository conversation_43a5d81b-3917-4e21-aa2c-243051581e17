import React from 'react';

export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

export interface TableAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: (record: any, index: number) => void;
}

export interface ToolbarAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: () => void;
}

export interface SearchField {
  key: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'dateRange';
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
}

export interface TabItem {
  key: string;
  label: string;
  count?: number;
}

export interface TableViewWithSearchProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  columns: TableColumn[];
  dataSource: any[];
  loading?: boolean;
  searchFields?: SearchField[];
  toolbarActions?: ToolbarAction[];
  rowActions?: TableAction[];
  tabs?: TabItem[];
  activeTab?: string;
  onTabChange?: (key: string) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showTotal?: boolean;
  };
  rowSelection?: {
    type: 'checkbox' | 'radio';
    selectedRowKeys?: string[];
    onChange?: (selectedRowKeys: string[], selectedRows: any[]) => void;
  };
  style?: React.CSSProperties;
  className?: string;
  onSearch?: (searchValues: Record<string, any>) => void;
  onTableChange?: (pagination: any, filters: any, sorter: any) => void;
  onRow?: (record: any, index: number) => React.HTMLAttributes<HTMLTableRowElement>;
}

export const TableViewWithSearch: React.FC<TableViewWithSearchProps> = ({
  title,
  columns,
  dataSource,
  loading = false,
  searchFields = [],
  toolbarActions = [],
  rowActions = [],
  tabs = [],
  activeTab,
  onTabChange,
  pagination,
  rowSelection,
  style,
  className,
  onSearch,
  onTableChange,
  onRow,
  ...rest
}) => {
  const [searchValues, setSearchValues] = React.useState<Record<string, any>>({});
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>(
    rowSelection?.selectedRowKeys || []
  );
  const [sortConfig, setSortConfig] = React.useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // 处理搜索
  const handleSearch = () => {
    onSearch?.(searchValues);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValues({});
    onSearch?.({});
  };

  // 处理搜索字段变化
  const handleSearchFieldChange = (key: string, value: any) => {
    const newValues = { ...searchValues, [key]: value };
    setSearchValues(newValues);
  };

  // 处理行选择
  const handleRowSelection = (keys: string[], rows: any[]) => {
    setSelectedRowKeys(keys);
    rowSelection?.onChange?.(keys, rows);
  };

  // 处理排序
  const handleSort = (column: TableColumn) => {
    if (!column.sortable) return;

    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig?.key === column.key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }

    setSortConfig({ key: column.key, direction });
    onTableChange?.(pagination, {}, { field: column.key, order: direction });
  };

  // 渲染页面标题
  const renderTitle = () => {
    if (!title) return null;

    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <h1 style={{
          margin: 0,
          fontSize: '16px',
          fontWeight: 500,
          color: '#1f2937'
        }}>
          {title}
        </h1>

        {/* 右侧工具栏 */}
        {toolbarActions.length > 0 && (
          <div style={{ display: 'flex', gap: '8px' }}>
            {toolbarActions.map(action => (
              <button
                key={action.key}
                onClick={action.onClick}
                disabled={action.disabled}
                style={{
                  height: '32px',
                  padding: '0 16px',
                  backgroundColor: action.type === 'primary' ? '#1677ff' : '#ffffff',
                  color: action.type === 'primary' ? '#ffffff' : '#374151',
                  border: action.type === 'primary' ? 'none' : '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: action.disabled ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  fontWeight: 400,
                  opacity: action.disabled ? 0.6 : 1,
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!action.disabled) {
                    e.currentTarget.style.backgroundColor = action.type === 'primary' ? '#0f5fff' : '#f9fafb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!action.disabled) {
                    e.currentTarget.style.backgroundColor = action.type === 'primary' ? '#1677ff' : '#ffffff';
                  }
                }}
              >
                {action.icon && <span style={{ marginRight: '6px' }}>{action.icon}</span>}
                {action.label}
              </button>
            ))}
          </div>
        )}
      </div>
    );
  };

  // 渲染搜索表单
  const renderSearchForm = () => {
    if (searchFields.length === 0) return null;

    return (
      <div style={{
        padding: '20px',
        backgroundColor: '#ffffff',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        marginBottom: '16px'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 1fr)',
          gap: '16px 20px',
          marginBottom: '20px'
        }}>
          {searchFields.map(field => (
            <div key={field.key}>
              <label style={{
                display: 'block',
                marginBottom: '6px',
                fontSize: '14px',
                color: '#374151',
                fontWeight: 400
              }}>
                {field.label}
              </label>
              {field.type === 'input' && (
                <input
                  type="text"
                  placeholder={field.placeholder}
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '32px',
                    padding: '6px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    backgroundColor: '#ffffff'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#1677ff';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(22, 119, 255, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              )}
              {field.type === 'select' && (
                <select
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '32px',
                    padding: '6px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    backgroundColor: '#ffffff'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#1677ff';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(22, 119, 255, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  <option value="">{field.placeholder || '请选择'}</option>
                  {field.options?.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              )}
            </div>
          ))}
        </div>

        <div style={{ display: 'flex', gap: '12px' }}>
          <button
            onClick={handleSearch}
            style={{
              height: '32px',
              padding: '0 20px',
              backgroundColor: '#1677ff',
              color: '#ffffff',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 400
            }}
          >
            查询
          </button>
          <button
            onClick={handleReset}
            style={{
              height: '32px',
              padding: '0 20px',
              backgroundColor: '#ffffff',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 400
            }}
          >
            重置
          </button>
        </div>
      </div>
    );
  };

  // 渲染标签页
  const renderTabs = () => {
    if (tabs.length === 0) return null;

    return (
      <div style={{
        borderBottom: '1px solid #e5e7eb',
        marginBottom: '16px'
      }}>
        <div style={{
          display: 'flex',
          gap: '32px'
        }}>
          {tabs.map(tab => {
            const isActive = activeTab === tab.key;
            return (
              <button
                key={tab.key}
                onClick={() => onTabChange?.(tab.key)}
                style={{
                  padding: '12px 0',
                  backgroundColor: 'transparent',
                  border: 'none',
                  borderBottom: isActive ? '2px solid #1677ff' : '2px solid transparent',
                  color: isActive ? '#1677ff' : '#6b7280',
                  fontSize: '14px',
                  fontWeight: isActive ? 500 : 400,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.color = '#374151';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.color = '#6b7280';
                  }
                }}
              >
                <span>{tab.label}</span>
                {tab.count !== undefined && (
                  <span style={{
                    backgroundColor: isActive ? '#1677ff' : '#e5e7eb',
                    color: isActive ? '#ffffff' : '#6b7280',
                    padding: '2px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: 400,
                    minWidth: '20px',
                    textAlign: 'center'
                  }}>
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </div>
      </div>
    );
  };

  // 渲染更多操作按钮
  const renderMoreActions = () => {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          {rowSelection && selectedRowKeys.length > 0 && (
            <>
              <span style={{ fontSize: '14px', color: '#6b7280' }}>
                已选择 {selectedRowKeys.length} 项
              </span>
              <button
                style={{
                  height: '32px',
                  padding: '0 16px',
                  backgroundColor: '#ffffff',
                  color: '#374151',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                批量操作
              </button>
            </>
          )}
        </div>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          <button
            style={{
              height: '32px',
              padding: '0 16px',
              backgroundColor: '#ffffff',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}
          >
            <span>更多条件</span>
            <span style={{ fontSize: '12px' }}>▼</span>
          </button>

          <button
            style={{
              height: '32px',
              padding: '0 16px',
              backgroundColor: '#1677ff',
              color: '#ffffff',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            确定
          </button>
        </div>
      </div>
    );
  };

  // 渲染表格
  const renderTable = () => {
    return (
      <div style={{
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        overflow: 'hidden',
        backgroundColor: '#ffffff'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f9fafb' }}>
              {rowSelection && (
                <th style={{
                  padding: '12px 16px',
                  textAlign: 'left',
                  borderBottom: '1px solid #e5e7eb',
                  width: '48px',
                  fontSize: '14px',
                  fontWeight: 500,
                  color: '#374151'
                }}>
                  <input
                    type={rowSelection.type === 'radio' ? 'radio' : 'checkbox'}
                    checked={selectedRowKeys.length === dataSource.length && dataSource.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        const allKeys = dataSource.map((_, index) => index.toString());
                        handleRowSelection(allKeys, dataSource);
                      } else {
                        handleRowSelection([], []);
                      }
                    }}
                    style={{
                      width: '16px',
                      height: '16px'
                    }}
                  />
                </th>
              )}
              {columns.map(column => (
                <th
                  key={column.key}
                  style={{
                    padding: '12px 16px',
                    textAlign: column.align || 'left',
                    borderBottom: '1px solid #e5e7eb',
                    width: column.width,
                    cursor: column.sortable ? 'pointer' : 'default',
                    userSelect: 'none',
                    whiteSpace: 'nowrap',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#374151'
                  }}
                  onClick={() => handleSort(column)}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <span>{column.title}</span>
                    {column.sortable && (
                      <span style={{
                        fontSize: '12px',
                        color: '#9ca3af',
                        display: 'flex',
                        flexDirection: 'column',
                        lineHeight: '1'
                      }}>
                        {sortConfig?.key === column.key ? (
                          sortConfig.direction === 'asc' ? '▲' : '▼'
                        ) : (
                          <>
                            <span style={{ marginBottom: '-2px' }}>▲</span>
                            <span>▼</span>
                          </>
                        )}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              {rowActions.length > 0 && (
                <th style={{
                  padding: '12px 16px',
                  textAlign: 'center',
                  borderBottom: '1px solid #e5e7eb',
                  fontSize: '14px',
                  fontWeight: 500,
                  color: '#374151'
                }}>
                  操作
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                  style={{
                    padding: '48px',
                    textAlign: 'center',
                    color: '#9ca3af',
                    backgroundColor: '#ffffff',
                    fontSize: '14px'
                  }}
                >
                  加载中...
                </td>
              </tr>
            ) : dataSource.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                  style={{
                    padding: '48px',
                    textAlign: 'center',
                    color: '#9ca3af',
                    backgroundColor: '#ffffff',
                    fontSize: '14px'
                  }}
                >
                  暂无数据
                </td>
              </tr>
            ) : (
              dataSource.map((record, index) => {
                const rowKey = index.toString();
                const isSelected = selectedRowKeys.includes(rowKey);
                const rowProps = onRow?.(record, index) || {};

                return (
                  <tr
                    key={rowKey}
                    {...rowProps}
                    style={{
                      backgroundColor: isSelected ? '#eff6ff' : 'transparent',
                      borderBottom: index === dataSource.length - 1 ? 'none' : '1px solid #f3f4f6',
                      ...rowProps.style
                    }}
                    onMouseEnter={(e) => {
                      if (!isSelected) {
                        e.currentTarget.style.backgroundColor = '#f9fafb';
                      }
                      rowProps.onMouseEnter?.(e);
                    }}
                    onMouseLeave={(e) => {
                      if (!isSelected) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                      rowProps.onMouseLeave?.(e);
                    }}
                  >
                    {rowSelection && (
                      <td style={{
                        padding: '12px 16px',
                        borderBottom: 'none'
                      }}>
                        <input
                          type={rowSelection.type === 'radio' ? 'radio' : 'checkbox'}
                          checked={isSelected}
                          onChange={(e) => {
                            if (rowSelection.type === 'radio') {
                              handleRowSelection([rowKey], [record]);
                            } else {
                              if (e.target.checked) {
                                handleRowSelection([...selectedRowKeys, rowKey], [...selectedRowKeys.map(key => dataSource[parseInt(key)]), record]);
                              } else {
                                const newKeys = selectedRowKeys.filter(key => key !== rowKey);
                                handleRowSelection(newKeys, newKeys.map(key => dataSource[parseInt(key)]));
                              }
                            }
                          }}
                          style={{
                            width: '16px',
                            height: '16px'
                          }}
                        />
                      </td>
                    )}
                    {columns.map(column => {
                      const value = record[column.dataIndex];
                      const cellContent = column.render ? column.render(value, record, index) : value;

                      return (
                        <td
                          key={column.key}
                          style={{
                            padding: '12px 16px',
                            textAlign: column.align || 'left',
                            borderBottom: 'none',
                            fontSize: '14px',
                            color: '#374151'
                          }}
                        >
                          {cellContent}
                        </td>
                      );
                    })}
                    {rowActions.length > 0 && (
                      <td style={{
                        padding: '12px 16px',
                        textAlign: 'center',
                        borderBottom: 'none'
                      }}>
                        <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                          {rowActions.map(action => (
                            <button
                              key={action.key}
                              onClick={() => action.onClick?.(record, index)}
                              disabled={action.disabled}
                              style={{
                                height: '28px',
                                padding: '0 12px',
                                backgroundColor: action.type === 'primary' ? '#1677ff' :
                                                action.type === 'danger' ? '#ef4444' : 'transparent',
                                color: action.type === 'primary' || action.type === 'danger' ? '#ffffff' : '#1677ff',
                                border: action.type === 'primary' || action.type === 'danger' ? 'none' : '1px solid #1677ff',
                                borderRadius: '6px',
                                cursor: action.disabled ? 'not-allowed' : 'pointer',
                                fontSize: '12px',
                                fontWeight: 400,
                                opacity: action.disabled ? 0.6 : 1,
                                transition: 'all 0.2s'
                              }}
                              onMouseEnter={(e) => {
                                if (!action.disabled) {
                                  if (action.type === 'primary') {
                                    e.currentTarget.style.backgroundColor = '#0f5fff';
                                  } else if (action.type === 'danger') {
                                    e.currentTarget.style.backgroundColor = '#dc2626';
                                  } else {
                                    e.currentTarget.style.backgroundColor = '#eff6ff';
                                  }
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (!action.disabled) {
                                  if (action.type === 'primary') {
                                    e.currentTarget.style.backgroundColor = '#1677ff';
                                  } else if (action.type === 'danger') {
                                    e.currentTarget.style.backgroundColor = '#ef4444';
                                  } else {
                                    e.currentTarget.style.backgroundColor = 'transparent';
                                  }
                                }
                              }}
                            >
                              {action.icon && <span style={{ marginRight: '4px' }}>{action.icon}</span>}
                              {action.label}
                            </button>
                          ))}
                        </div>
                      </td>
                    )}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    );
  };

  // 渲染分页
  const renderPagination = () => {
    if (!pagination) return null;

    const { current, pageSize, total, showSizeChanger, showQuickJumper } = pagination;
    const totalPages = Math.ceil(total / pageSize);

    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: '16px',
        padding: '16px 0'
      }}>
        <div style={{ fontSize: '14px', color: '#6b7280' }}>
          共 {total} 条记录，第 {current} / {totalPages} 页
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {showSizeChanger && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontSize: '14px', color: '#374151' }}>每页显示</span>
              <select
                value={pageSize}
                onChange={(e) => {
                  const newPageSize = parseInt(e.target.value);
                  onTableChange?.({ ...pagination, pageSize: newPageSize, current: 1 }, {}, {});
                }}
                style={{
                  height: '32px',
                  padding: '0 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: '#ffffff'
                }}
              >
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span style={{ fontSize: '14px', color: '#374151' }}>条/页</span>
            </div>
          )}

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <button
              disabled={current <= 1}
              onClick={() => onTableChange?.({ ...pagination, current: current - 1 }, {}, {})}
              style={{
                height: '32px',
                padding: '0 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                backgroundColor: '#ffffff',
                color: '#374151',
                cursor: current <= 1 ? 'not-allowed' : 'pointer',
                opacity: current <= 1 ? 0.6 : 1,
                fontSize: '14px'
              }}
            >
              上一页
            </button>

            {showQuickJumper && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '14px', color: '#374151' }}>跳至</span>
                <input
                  type="number"
                  min={1}
                  max={totalPages}
                  style={{
                    width: '64px',
                    height: '32px',
                    padding: '0 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    textAlign: 'center'
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      const page = parseInt((e.target as HTMLInputElement).value);
                      if (page >= 1 && page <= totalPages) {
                        onTableChange?.({ ...pagination, current: page }, {}, {});
                      }
                    }
                  }}
                />
                <span style={{ fontSize: '14px', color: '#374151' }}>页</span>
              </div>
            )}

            <button
              disabled={current >= totalPages}
              onClick={() => onTableChange?.({ ...pagination, current: current + 1 }, {}, {})}
              style={{
                height: '32px',
                padding: '0 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                backgroundColor: '#ffffff',
                color: '#374151',
                cursor: current >= totalPages ? 'not-allowed' : 'pointer',
                opacity: current >= totalPages ? 0.6 : 1,
                fontSize: '14px'
              }}
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    );
  };

  const defaultStyle: React.CSSProperties = {
    backgroundColor: '#f8fafc',
    borderRadius: 0,
    padding: '24px',
    ...style,
  };

  return (
    <div className={`lowcode-table-view-with-search ${className || ''}`} style={defaultStyle} {...rest}>
      {renderTitle()}
      {renderSearchForm()}
      {renderTabs()}
      {renderMoreActions()}
      {renderTable()}
      {renderPagination()}
    </div>
  );
};
