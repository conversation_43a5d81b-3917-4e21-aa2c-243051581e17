import React from 'react';
import { tokens } from '../../theme/tokens';

export interface TopNavigationProps extends React.HTMLAttributes<HTMLElement> {
  logo?: {
    src?: string; // 支持图片链接或base64
    alt?: string;
    text?: string;
    href?: string;
    showDefaultIcon?: boolean; // 是否显示默认图标
    iconText?: string; // 默认图标中的文字，默认为"全"
  };
  menu?: Array<{
    key: string;
    label: string;
    href?: string;
    children?: Array<{
      key: string;
      label: string;
      href?: string;
    }>;
  }>;
  user?: {
    name?: string;
    avatar?: string;
    menu?: Array<{
      key: string;
      label: string;
      onClick?: () => void;
    }>;
  };
  actions?: Array<{
    key: string;
    label: string;
    icon?: string;
    onClick?: () => void;
  }>;
  style?: React.CSSProperties;
  className?: string;
  onMenuClick?: (key: string) => void;
  onUserMenuClick?: (key: string) => void;
}

export const TopNavigation: React.FC<TopNavigationProps> = ({
  logo,
  menu = [],
  user,
  actions = [],
  style,
  className,
  onMenuClick,
  onUserMenuClick,
  ...rest
}) => {
  const [activeMenu, setActiveMenu] = React.useState<string>('apis');
  const [showUserMenu, setShowUserMenu] = React.useState(false);

  const handleMenuClick = (key: string, href?: string) => {
    setActiveMenu(key);
    if (href) {
      window.location.href = href;
    }
    onMenuClick?.(key);
  };

  const handleUserMenuClick = (key: string) => {
    setShowUserMenu(false);
    onUserMenuClick?.(key);
  };

  const defaultStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 16px',
    height: '48px',
    backgroundColor: '#1f2937', // 深灰色背景
    color: '#ffffff',
    borderBottom: 'none',
    ...style,
  };

  return (
    <nav className={`lowcode-top-navigation ${className || ''}`} style={defaultStyle} {...rest}>
      {/* Logo区域 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {logo && (
          <div style={{ display: 'flex', alignItems: 'center', marginRight: '40px' }}>
            {logo.href ? (
              <a
                href={logo.href}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  textDecoration: 'none',
                  color: 'inherit',
                }}
              >
                {/* Logo图标渲染逻辑 */}
                {logo.src ? (
                  // 如果有图片源，显示图片
                  <img
                    src={logo.src}
                    alt={logo.alt || 'Logo'}
                    style={{
                      height: '32px',
                      maxWidth: '120px',
                      objectFit: 'contain',
                      marginRight: logo.text ? '8px' : '0'
                    }}
                  />
                ) : (logo.showDefaultIcon !== false) ? (
                  // 如果没有图片源且未禁用默认图标，显示默认图标
                  <div style={{
                    width: '24px',
                    height: '24px',
                    backgroundColor: '#ffffff',
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '8px',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    color: '#1f2937'
                  }}>
                    {logo.iconText || '全'}
                  </div>
                ) : null}

                {logo.text && (
                  <span style={{ fontSize: '16px', fontWeight: 600, color: '#ffffff' }}>
                    {logo.text}
                  </span>
                )}
              </a>
            ) : (
              <>
                {/* Logo图标渲染逻辑 */}
                {logo.src ? (
                  // 如果有图片源，显示图片
                  <img
                    src={logo.src}
                    alt={logo.alt || 'Logo'}
                    style={{
                      height: '32px',
                      maxWidth: '120px',
                      objectFit: 'contain',
                      marginRight: logo.text ? '8px' : '0'
                    }}
                  />
                ) : (logo.showDefaultIcon !== false) ? (
                  // 如果没有图片源且未禁用默认图标，显示默认图标
                  <div style={{
                    width: '24px',
                    height: '24px',
                    backgroundColor: '#ffffff',
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '8px',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    color: '#1f2937'
                  }}>
                    {logo.iconText || '全'}
                  </div>
                ) : null}

                {logo.text && (
                  <span style={{ fontSize: '16px', fontWeight: 600, color: '#ffffff' }}>
                    {logo.text}
                  </span>
                )}
              </>
            )}
          </div>
        )}

        {/* 主菜单 */}
        <div style={{ display: 'flex', alignItems: 'center', marginLeft: '24px' }}>
          {menu.map((item) => (
            <div key={item.key} style={{ position: 'relative' }}>
              <button
                style={{
                  background: activeMenu === item.key ? '#3b82f6' : 'transparent',
                  border: 'none',
                  color: activeMenu === item.key ? '#ffffff' : '#d1d5db',
                  fontSize: '14px',
                  padding: '6px 16px',
                  cursor: 'pointer',
                  borderRadius: '4px',
                  transition: 'all 0.2s',
                  fontWeight: activeMenu === item.key ? '500' : '400',
                  marginRight: '4px'
                }}
                onClick={() => handleMenuClick(item.key, item.href)}
                onMouseEnter={(e) => {
                  if (activeMenu !== item.key) {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.color = '#ffffff';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeMenu !== item.key) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#d1d5db';
                  }
                }}
              >
                {item.label}
              </button>

              {/* 子菜单 */}
              {item.children && item.children.length > 0 && (
                <div
                  style={{
                    position: 'absolute',
                    top: '100%',
                    left: '0',
                    backgroundColor: '#ffffff',
                    boxShadow: '0 10px 24px rgba(0, 0, 0, 0.12)',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    minWidth: '200px',
                    zIndex: 1000,
                    padding: '6px',
                    display: activeMenu === item.key ? 'block' : 'none',
                  }}
                >
                  {item.children.map((child) => (
                    <button
                      key={child.key}
                      className={"lc-btn lc-btn-default"}
                      style={{ display: 'block', width: '100%', textAlign: 'left', background: 'transparent', border: 'none', padding: '8px 10px' }}
                      onClick={() => handleMenuClick(child.key, child.href)}
                    >
                      {child.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 右侧区域 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {/* 搜索图标 */}
        <button
          style={{
            background: 'transparent',
            border: 'none',
            color: '#d1d5db',
            cursor: 'pointer',
            padding: '8px',
            borderRadius: '4px',
            marginRight: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            e.currentTarget.style.color = '#ffffff';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = '#d1d5db';
          }}
        >
          <span className="material-icons" style={{ fontSize: '20px' }}>search</span>
        </button>

        {/* 通知图标 */}
        <button
          style={{
            background: 'transparent',
            border: 'none',
            color: '#d1d5db',
            cursor: 'pointer',
            padding: '8px',
            borderRadius: '4px',
            marginRight: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            e.currentTarget.style.color = '#ffffff';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = '#d1d5db';
          }}
        >
          <span className="material-icons" style={{ fontSize: '20px' }}>notifications</span>
          {/* 通知红点 */}
          <div style={{
            position: 'absolute',
            top: '6px',
            right: '6px',
            width: '8px',
            height: '8px',
            backgroundColor: '#ef4444',
            borderRadius: '50%'
          }}></div>
        </button>

        {/* 操作按钮 */}
        {actions.map((action) => (
          <button
            key={action.key}
            style={{
              background: 'transparent',
              border: 'none',
              color: '#d1d5db',
              cursor: 'pointer',
              padding: '6px 12px',
              borderRadius: '4px',
              marginRight: '8px',
              fontSize: '14px'
            }}
            onClick={action.onClick}
          >
            {action.icon && <span style={{ marginRight: '6px' }}>{action.icon}</span>}
            {action.label}
          </button>
        ))}

        {/* 用户信息 */}
        {user && (
          <div style={{ position: 'relative' }}>
            <button
              style={{
                display: 'flex',
                alignItems: 'center',
                background: 'transparent',
                border: 'none',
                color: '#ffffff',
                cursor: 'pointer',
                padding: '6px 8px',
                borderRadius: '4px',
                fontSize: '14px',
                fontWeight: '500'
              }}
              onClick={() => setShowUserMenu(!showUserMenu)}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              {user.avatar && (
                <img
                  src={user.avatar}
                  alt="User Avatar"
                  style={{
                    width: '24px',
                    height: '24px',
                    borderRadius: '50%',
                    marginRight: user.name ? '8px' : '0',
                  }}
                />
              )}
              {user.name && <span style={{ marginRight: '4px' }}>{user.name}</span>}
              <span className="material-icons" style={{ fontSize: '16px', color: '#d1d5db' }}>
                expand_more
              </span>
            </button>

            {/* 用户菜单 */}
            {user.menu && user.menu.length > 0 && showUserMenu && (
              <div
                style={{
                  position: 'absolute',
                  top: '100%',
                  right: '0',
                  backgroundColor: '#ffffff',
                  boxShadow: '0 10px 24px rgba(0, 0, 0, 0.12)',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  minWidth: '200px',
                  zIndex: 1000,
                }}
              >
                {user.menu.map((item) => (
                  <button
                    key={item.key}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '8px 16px',
                      border: 'none',
                      background: 'none',
                      textAlign: 'left',
                      color: '#000000',
                      fontSize: '14px',
                      cursor: 'pointer',
                      transition: 'background-color 0.3s',
                    }}
                    onClick={() => {
                      item.onClick?.();
                      handleUserMenuClick(item.key);
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f5f5f5';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </nav>
  );
};
