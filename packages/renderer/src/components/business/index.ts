import { ComponentMeta } from '../../types';
import { TopNavigation } from './TopNavigation';
import { SidebarTreeView } from './SidebarTreeView';
import { TableViewWithSearch } from './TableViewWithSearch';
import { StatusBar } from './StatusBar';
import { ComponentLibrarySidebar } from './ComponentLibrarySidebar';

// 导出组件
export { TopNavigation, SidebarTreeView, TableViewWithSearch, StatusBar, ComponentLibrarySidebar };

// TopNavigation组件元数据
export const topNavigationMeta: ComponentMeta = {
  type: 'TopNavigation',
  name: '顶部导航',
  description: '顶部导航组件，包含Logo、菜单、用户信息等',
  category: 'business',
  icon: 'navigation',
  props: [
    {
      name: 'logo',
      type: 'object',
      description: 'Logo配置',
      default: {
        text: '系统名称',
        href: '/',
        showDefaultIcon: true,
        iconText: '全'
      },
      properties: [
        {
          name: 'src',
          type: 'string',
          description: 'Logo图片地址（支持URL或base64）'
        },
        {
          name: 'alt',
          type: 'string',
          description: '图片替代文本'
        },
        {
          name: 'text',
          type: 'string',
          description: 'Logo文字'
        },
        {
          name: 'href',
          type: 'string',
          description: 'Logo链接地址'
        },
        {
          name: 'showDefaultIcon',
          type: 'boolean',
          description: '是否显示默认图标',
          default: true
        },
        {
          name: 'iconText',
          type: 'string',
          description: '默认图标文字',
          default: '全'
        }
      ]
    },
    {
      name: 'menu',
      type: 'array',
      description: '主菜单配置',
      default: [
        { key: 'home', label: '首页', href: '/' },
        { key: 'about', label: '关于' }
      ]
    },
    {
      name: 'user',
      type: 'object',
      description: '用户信息配置',
      default: {
        name: '用户名',
        menu: [
          { key: 'profile', label: '个人资料' },
          { key: 'logout', label: '退出登录' }
        ]
      }
    },
    {
      name: 'actions',
      type: 'array',
      description: '操作按钮配置',
      default: []
    },
    {
      name: 'style',
      type: 'object',
      description: '自定义样式'
    },
    {
      name: 'className',
      type: 'string',
      description: 'CSS类名'
    }
  ],
  events: [
    {
      name: 'onMenuClick',
      description: '菜单点击事件',
      params: [
        { name: 'key', type: 'string', description: '菜单项key' }
      ]
    },
    {
      name: 'onUserMenuClick',
      description: '用户菜单点击事件',
      params: [
        { name: 'key', type: 'string', description: '菜单项key' }
      ]
    }
  ],
  defaultProps: {
    logo: {
      text: '低代码平台',
      href: '/',
      showDefaultIcon: true,
      iconText: '全'
    },
    menu: [
      { key: 'dashboard', label: '仪表板', href: '/dashboard' },
      { key: 'components', label: '组件库', href: '/components' },
      { key: 'templates', label: '模板', href: '/templates' }
    ],
    user: {
      name: '管理员',
      menu: [
        { key: 'profile', label: '个人资料' },
        { key: 'settings', label: '系统设置' },
        { key: 'logout', label: '退出登录' }
      ]
    },
    actions: [
      { key: 'help', label: '帮助', icon: '?' },
      { key: 'notification', label: '通知', icon: '🔔' }
    ]
  }
};

// SidebarTreeView组件元数据
export const sidebarTreeViewMeta: ComponentMeta = {
  type: 'SidebarTreeView',
  name: '侧边栏树形导航',
  description: '侧边栏树形导航组件，支持搜索和层级展开',
  category: 'business',
  icon: 'tree',
  props: [
    {
      name: 'data',
      type: 'array',
      required: true,
      description: '树形数据',
      default: []
    },
    {
      name: 'width',
      type: 'number',
      description: '宽度',
      default: 240
    },
    {
      name: 'height',
      type: 'string',
      description: '高度',
      default: '100%'
    },
    {
      name: 'searchable',
      type: 'boolean',
      description: '是否可搜索',
      default: true
    },
    {
      name: 'searchPlaceholder',
      type: 'string',
      description: '搜索框占位符',
      default: '搜索...'
    },
    {
      name: 'defaultExpandedKeys',
      type: 'array',
      description: '默认展开的节点',
      default: []
    },
    {
      name: 'defaultSelectedKeys',
      type: 'array',
      description: '默认选中的节点',
      default: []
    },
    {
      name: 'showIcon',
      type: 'boolean',
      description: '是否显示图标',
      default: true
    }
  ],
  events: [
    {
      name: 'onSelect',
      description: '节点选择事件',
      params: [
        { name: 'selectedKeys', type: 'array', description: '选中的节点key数组' },
        { name: 'node', type: 'object', description: '选中的节点对象' }
      ]
    },
    {
      name: 'onExpand',
      description: '节点展开事件',
      params: [
        { name: 'expandedKeys', type: 'array', description: '展开的节点key数组' },
        { name: 'node', type: 'object', description: '操作的节点对象' }
      ]
    },
    {
      name: 'onSearch',
      description: '搜索事件',
      params: [
        { name: 'value', type: 'string', description: '搜索值' }
      ]
    }
  ],
  defaultProps: {
    data: [
      {
        key: 'dashboard',
        title: '仪表板',
        icon: '📊',
        href: '/dashboard'
      },
      {
        key: 'components',
        title: '组件管理',
        icon: '🧩',
        children: [
          { key: 'basic', title: '基础组件', icon: '🔧', href: '/components/basic' },
          { key: 'business', title: '业务组件', icon: '💼', href: '/components/business' },
          { key: 'custom', title: '自定义组件', icon: '⚙️', href: '/components/custom' }
        ]
      },
      {
        key: 'pages',
        title: '页面管理',
        icon: '📄',
        children: [
          { key: 'list', title: '页面列表', icon: '📋', href: '/pages/list' },
          { key: 'templates', title: '页面模板', icon: '📝', href: '/pages/templates' }
        ]
      },
      {
        key: 'settings',
        title: '系统设置',
        icon: '⚙️',
        children: [
          { key: 'users', title: '用户管理', icon: '👥', href: '/settings/users' },
          { key: 'roles', title: '角色管理', icon: '🔐', href: '/settings/roles' },
          { key: 'system', title: '系统配置', icon: '🔧', href: '/settings/system' }
        ]
      }
    ],
    width: 240,
    height: '100%',
    searchable: true,
    searchPlaceholder: '搜索菜单...',
    showIcon: true
  }
};

// TableViewWithSearch组件元数据
export const tableViewWithSearchMeta: ComponentMeta = {
  type: 'TableViewWithSearch',
  name: '表格搜索视图',
  description: '集成搜索、标签页、工具栏、表格功能的复合组件，支持像素级UI还原',
  category: 'business',
  icon: 'table',
  props: [
    {
      name: 'title',
      type: 'string',
      description: '页面标题',
      default: ''
    },
    {
      name: 'columns',
      type: 'array',
      required: true,
      description: '表格列配置',
      default: []
    },
    {
      name: 'dataSource',
      type: 'array',
      required: true,
      description: '表格数据源',
      default: []
    },
    {
      name: 'loading',
      type: 'boolean',
      description: '加载状态',
      default: false
    },
    {
      name: 'searchFields',
      type: 'array',
      description: '搜索字段配置',
      default: []
    },
    {
      name: 'toolbarActions',
      type: 'array',
      description: '工具栏操作按钮',
      default: []
    },
    {
      name: 'rowActions',
      type: 'array',
      description: '行操作按钮',
      default: []
    },
    {
      name: 'tabs',
      type: 'array',
      description: '标签页配置',
      default: []
    },
    {
      name: 'activeTab',
      type: 'string',
      description: '当前活跃的标签页',
      default: ''
    },
    {
      name: 'pagination',
      type: 'object',
      description: '分页配置'
    },
    {
      name: 'rowSelection',
      type: 'object',
      description: '行选择配置'
    }
  ],
  events: [
    {
      name: 'onSearch',
      description: '搜索事件',
      params: [
        { name: 'searchValues', type: 'object', description: '搜索值对象' }
      ]
    },
    {
      name: 'onTableChange',
      description: '表格变化事件',
      params: [
        { name: 'pagination', type: 'object', description: '分页信息' },
        { name: 'filters', type: 'object', description: '筛选信息' },
        { name: 'sorter', type: 'object', description: '排序信息' }
      ]
    },
    {
      name: 'onTabChange',
      description: '标签页切换事件',
      params: [
        { name: 'key', type: 'string', description: '标签页key' }
      ]
    }
  ],
  defaultProps: {
    title: '全部API',
    columns: [
      {
        key: 'path',
        title: '路径',
        dataIndex: 'path',
        width: '200px',
        render: (value: string) => `<span style="color: #ef4444; font-weight: 500;">${value}</span>`
      },
      { key: 'apiLevel', title: 'API敏感等级', dataIndex: 'apiLevel', width: '120px', align: 'center' },
      { key: 'riskLevel', title: 'API风险等级', dataIndex: 'riskLevel', width: '120px', align: 'center' },
      {
        key: 'callCount',
        title: '累计调用次数',
        dataIndex: 'callCount',
        width: '120px',
        align: 'center',
        sortable: true
      },
      { key: 'sourceIp', title: '流量来源', dataIndex: 'sourceIp', width: '120px', align: 'center' },
      {
        key: 'lastCallTime',
        title: '首次发现时间',
        dataIndex: 'lastCallTime',
        width: '160px',
        align: 'center',
        sortable: true
      }
    ],
    dataSource: [
      {
        id: 1,
        path: '/login',
        apiLevel: '高敏感',
        riskLevel: '高风险',
        callCount: '3.6千',
        sourceIp: '***********',
        lastCallTime: '2025-08-14 19:18:12'
      },
      {
        id: 2,
        path: '/abnormal',
        apiLevel: '高敏感',
        riskLevel: '高风险',
        callCount: '2.5千',
        sourceIp: '***********',
        lastCallTime: '2025-08-14 19:19:23'
      }
    ],
    searchFields: [
      { key: 'application', label: '应用', type: 'input', placeholder: '请输入' },
      { key: 'appName', label: '应用名称', type: 'input', placeholder: '请输入' },
      {
        key: 'domain',
        label: '访问域',
        type: 'select',
        placeholder: '或',
        options: [
          { label: '选项1', value: 'option1' },
          { label: '选项2', value: 'option2' }
        ]
      },
      {
        key: 'terminalType',
        label: '终端类型',
        type: 'select',
        placeholder: '或',
        options: [
          { label: '类型1', value: 'type1' },
          { label: '类型2', value: 'type2' }
        ]
      },
      { key: 'responseType', label: '响应类型', type: 'input', placeholder: '请选择' },
      {
        key: 'apiGroup',
        label: 'API组',
        type: 'select',
        placeholder: '或',
        options: [
          { label: '组1', value: 'group1' },
          { label: '组2', value: 'group2' }
        ]
      },
      { key: 'requestType', label: '请求类型', type: 'input', placeholder: '请选择' },
      {
        key: 'responseDataType',
        label: '响应数据标签',
        type: 'select',
        placeholder: '或',
        options: [
          { label: '标签1', value: 'label1' },
          { label: '标签2', value: 'label2' }
        ]
      }
    ],
    tabs: [
      { key: 'high', label: '高风险', count: 34 },
      { key: 'medium', label: '中风险', count: 38 },
      { key: 'low', label: '低风险', count: 8 },
      { key: 'none', label: '无风险', count: 124 },
      { key: 'other', label: '其他', count: 8 },
      { key: 'uncategorized', label: '取消分组', count: 0 }
    ],
    activeTab: 'high',
    toolbarActions: [
      { key: 'batch', label: '批量操作', type: 'default' },
      { key: 'create', label: '新建', type: 'primary' }
    ],
    rowActions: [
      { key: 'view', label: '查看', type: 'default', icon: '👁' },
      { key: 'more', label: '更多', type: 'default', icon: '⋯' }
    ],
    pagination: {
      current: 1,
      pageSize: 25,
      total: 34,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: true
    },
    rowSelection: {
      type: 'checkbox'
    }
  }
};

// StatusBar组件元数据
export const statusBarMeta: ComponentMeta = {
  type: 'StatusBar',
  name: '状态栏',
  description: '底部状态栏组件，显示系统状态信息',
  category: 'business',
  icon: 'status',
  props: [
    {
      name: 'items',
      type: 'array',
      required: true,
      description: '状态项配置',
      default: []
    },
    {
      name: 'position',
      type: 'string',
      description: '定位方式',
      default: 'fixed',
      options: [
        { label: '固定定位', value: 'fixed' },
        { label: '相对定位', value: 'relative' }
      ]
    },
    {
      name: 'height',
      type: 'number',
      description: '高度',
      default: 32
    },
    {
      name: 'backgroundColor',
      type: 'string',
      description: '背景颜色',
      default: '#f5f5f5'
    },
    {
      name: 'textColor',
      type: 'string',
      description: '文字颜色',
      default: '#666666'
    },
    {
      name: 'borderTop',
      type: 'string',
      description: '顶部边框',
      default: '1px solid #e8e8e8'
    },
    {
      name: 'showSeparator',
      type: 'boolean',
      description: '显示分隔符',
      default: true
    }
  ],
  events: [
    {
      name: 'onItemClick',
      description: '状态项点击事件',
      params: [
        { name: 'item', type: 'object', description: '点击的状态项' }
      ]
    }
  ],
  defaultProps: {
    items: [
      { key: 'ready', label: '状态', value: '就绪', icon: '✅', color: '#52c41a' },
      { key: 'users', label: '在线用户', value: 128, icon: '👥', clickable: true },
      { key: 'memory', label: '内存使用', value: '45%', icon: '💾' },
      { key: 'time', label: '当前时间', value: new Date().toLocaleTimeString(), icon: '🕐' }
    ],
    position: 'fixed',
    height: 32,
    backgroundColor: '#f5f5f5',
    textColor: '#666666',
    borderTop: '1px solid #e8e8e8',
    showSeparator: true
  }
};

// 业务组件元数据集合
export const businessComponentMetas: ComponentMeta[] = [
  topNavigationMeta,
  sidebarTreeViewMeta,
  tableViewWithSearchMeta,
  statusBarMeta
];

// 业务组件映射
export const businessComponents = {
  TopNavigation,
  SidebarTreeView,
  TableViewWithSearch,
  StatusBar
};
