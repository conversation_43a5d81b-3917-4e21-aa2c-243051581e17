import React from 'react';
import { TableViewWithSearch } from '../components/business/TableViewWithSearch';
import { tableViewWithSearchMeta } from '../components/business';

/**
 * TableViewWithSearch组件演示
 * 展示如何在低代码平台中使用TableViewWithSearch组件
 */
export const TableViewWithSearchDemo: React.FC = () => {
  // 使用组件元数据中的默认属性
  const props = tableViewWithSearchMeta.defaultProps;

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#f8fafc', 
      minHeight: '100vh' 
    }}>
      <h1 style={{ 
        marginBottom: '20px', 
        fontSize: '24px', 
        fontWeight: 600,
        color: '#1f2937'
      }}>
        TableViewWithSearch 组件演示
      </h1>
      
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        padding: '20px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <h2 style={{ 
          marginBottom: '16px', 
          fontSize: '18px', 
          fontWeight: 500,
          color: '#374151'
        }}>
          API管理界面
        </h2>
        
        <p style={{ 
          marginBottom: '20px', 
          color: '#6b7280',
          fontSize: '14px'
        }}>
          这是一个完整的API管理界面，包含搜索、标签页、表格和分页功能。
          该组件已集成到低代码平台中，可以通过拖拽方式添加到页面中。
        </p>

        <TableViewWithSearch
          {...props}
          onSearch={(values) => {
            console.log('搜索参数:', values);
          }}
          onTabChange={(key) => {
            console.log('切换标签页:', key);
          }}
          onTableChange={(pagination, filters, sorter) => {
            console.log('表格变化:', { pagination, filters, sorter });
          }}
        />
      </div>

      <div style={{
        marginTop: '20px',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        padding: '20px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <h2 style={{ 
          marginBottom: '16px', 
          fontSize: '18px', 
          fontWeight: 500,
          color: '#374151'
        }}>
          组件特性
        </h2>
        
        <ul style={{ 
          color: '#6b7280',
          fontSize: '14px',
          lineHeight: '1.6'
        }}>
          <li>✅ 页面标题和工具栏</li>
          <li>✅ 4列网格搜索表单</li>
          <li>✅ 标签页切换（带数量徽章）</li>
          <li>✅ 批量操作和更多条件</li>
          <li>✅ 现代化表格设计</li>
          <li>✅ 完整的分页功能</li>
          <li>✅ 行选择和排序</li>
          <li>✅ 响应式布局</li>
          <li>✅ 像素级UI还原</li>
          <li>✅ 低代码平台集成</li>
        </ul>
      </div>
    </div>
  );
};

export default TableViewWithSearchDemo;
