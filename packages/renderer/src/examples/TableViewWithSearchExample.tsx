import React, { useState } from 'react';
import { TableViewWithSearch, TableColumn, SearchField, ToolbarAction, TableAction, TabItem } from '../components/business/TableViewWithSearch';

// 模拟API数据
const mockData = [
  {
    id: 1,
    path: '/login',
    apiLevel: '高敏感',
    riskLevel: '高风险',
    callCount: '3.6千',
    sourceIp: '***********',
    lastCallTime: '2025-08-14 19:18:12',
    status: '高敏感'
  },
  {
    id: 2,
    path: '/abnormal',
    apiLevel: '高敏感',
    riskLevel: '高风险',
    callCount: '2.5千',
    sourceIp: '***********',
    lastCallTime: '2025-08-14 19:19:23',
    status: '高敏感'
  }
];

const TableViewWithSearchExample: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [searchValues, setSearchValues] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 25,
    total: 34
  });

  // 定义列
  const columns: TableColumn[] = [
    {
      key: 'path',
      title: '路径',
      dataIndex: 'path',
      width: '200px',
      render: (value) => (
        <span style={{ color: '#ef4444', fontWeight: 500 }}>{value}</span>
      )
    },
    {
      key: 'apiLevel',
      title: 'API敏感等级',
      dataIndex: 'apiLevel',
      width: '120px',
      align: 'center'
    },
    {
      key: 'riskLevel',
      title: 'API风险等级',
      dataIndex: 'riskLevel',
      width: '120px',
      align: 'center'
    },
    {
      key: 'callCount',
      title: '累计调用次数',
      dataIndex: 'callCount',
      width: '120px',
      align: 'center',
      sortable: true,
      render: (value) => (
        <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '4px' }}>
          {value}
          <span style={{ fontSize: '12px', color: '#9ca3af' }}>↓</span>
        </span>
      )
    },
    {
      key: 'sourceIp',
      title: '流量来源',
      dataIndex: 'sourceIp',
      width: '120px',
      align: 'center'
    },
    {
      key: 'lastCallTime',
      title: '首次发现时间',
      dataIndex: 'lastCallTime',
      width: '160px',
      align: 'center',
      sortable: true,
      render: (value) => (
        <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '4px' }}>
          {value}
          <span style={{ fontSize: '12px', color: '#9ca3af' }}>↓</span>
        </span>
      )
    },
    {
      key: 'actions',
      title: '操作',
      dataIndex: 'actions',
      width: '100px',
      align: 'center'
    }
  ];

  // 定义搜索字段
  const searchFields: SearchField[] = [
    {
      key: 'application',
      label: '应用',
      type: 'input',
      placeholder: '请输入'
    },
    {
      key: 'appName',
      label: '应用名称',
      type: 'input',
      placeholder: '请输入'
    },
    {
      key: 'domain',
      label: '访问域',
      type: 'select',
      placeholder: '或',
      options: [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' }
      ]
    },
    {
      key: 'terminalType',
      label: '终端类型',
      type: 'select',
      placeholder: '或',
      options: [
        { label: '类型1', value: 'type1' },
        { label: '类型2', value: 'type2' }
      ]
    },
    {
      key: 'responseType',
      label: '响应类型',
      type: 'input',
      placeholder: '请选择'
    },
    {
      key: 'apiGroup',
      label: 'API除',
      type: 'select',
      placeholder: '或',
      options: [
        { label: '组1', value: 'group1' },
        { label: '组2', value: 'group2' }
      ]
    },
    {
      key: 'requestType',
      label: '请求类型',
      type: 'input',
      placeholder: '请选择'
    },
    {
      key: 'responseDataType',
      label: '响应数据标签',
      type: 'select',
      placeholder: '或',
      options: [
        { label: '标签1', value: 'label1' },
        { label: '标签2', value: 'label2' }
      ]
    }
  ];

  // 定义工具栏操作
  const toolbarActions: ToolbarAction[] = [
    {
      key: 'batch',
      label: '批量操作',
      type: 'default'
    },
    {
      key: 'create',
      label: '新建',
      type: 'primary'
    }
  ];

  // 定义行操作
  const rowActions: TableAction[] = [
    {
      key: 'view',
      label: '查看',
      icon: '👁',
      type: 'default'
    },
    {
      key: 'more',
      label: '更多',
      icon: '⋯',
      type: 'default'
    }
  ];

  // 定义标签页
  const tabs: TabItem[] = [
    { key: 'all', label: '高风险', count: 34 },
    { key: 'medium', label: '中风险', count: 38 },
    { key: 'low', label: '低风险', count: 8 },
    { key: 'none', label: '无风险', count: 124 },
    { key: 'other', label: '其他', count: 8 },
    { key: 'uncategorized', label: '取消分组', count: 0 }
  ];

  const handleSearch = (values: Record<string, any>) => {
    setSearchValues(values);
    console.log('搜索参数:', values);
  };

  const handleTableChange = (paginationInfo: any, filters: any, sorter: any) => {
    setPagination(paginationInfo);
    console.log('表格变化:', { pagination: paginationInfo, filters, sorter });
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    console.log('切换标签页:', key);
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      <TableViewWithSearch
        title="全部API"
        columns={columns}
        dataSource={mockData}
        searchFields={searchFields}
        toolbarActions={toolbarActions}
        rowActions={rowActions}
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: true
        }}
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys: [],
          onChange: (keys, rows) => {
            console.log('选择行:', keys, rows);
          }
        }}
        onSearch={handleSearch}
        onTableChange={handleTableChange}
      />
    </div>
  );
};

export default TableViewWithSearchExample;
