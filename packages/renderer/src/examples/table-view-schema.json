{"id": "api-management-page", "title": "API管理页面", "components": [{"id": "table_view_with_search_001", "type": "TableViewWithSearch", "props": {"title": "全部API", "columns": [{"key": "path", "title": "路径", "dataIndex": "path", "width": "200px", "render": "{{(value) => `<span style=\"color: #ef4444; font-weight: 500;\">${value}</span>`}}"}, {"key": "apiLevel", "title": "API敏感等级", "dataIndex": "apiLevel", "width": "120px", "align": "center"}, {"key": "riskLevel", "title": "API风险等级", "dataIndex": "riskLevel", "width": "120px", "align": "center"}, {"key": "callCount", "title": "累计调用次数", "dataIndex": "callCount", "width": "120px", "align": "center", "sortable": true}, {"key": "sourceIp", "title": "流量来源", "dataIndex": "sourceIp", "width": "120px", "align": "center"}, {"key": "lastCallTime", "title": "首次发现时间", "dataIndex": "lastCallTime", "width": "160px", "align": "center", "sortable": true}], "dataSource": "{{api.getApiList}}", "searchFields": [{"key": "application", "label": "应用", "type": "input", "placeholder": "请输入"}, {"key": "appName", "label": "应用名称", "type": "input", "placeholder": "请输入"}, {"key": "domain", "label": "访问域", "type": "select", "placeholder": "或", "options": [{"label": "选项1", "value": "option1"}, {"label": "选项2", "value": "option2"}]}, {"key": "terminalType", "label": "终端类型", "type": "select", "placeholder": "或", "options": [{"label": "类型1", "value": "type1"}, {"label": "类型2", "value": "type2"}]}, {"key": "responseType", "label": "响应类型", "type": "input", "placeholder": "请选择"}, {"key": "apiGroup", "label": "API组", "type": "select", "placeholder": "或", "options": [{"label": "组1", "value": "group1"}, {"label": "组2", "value": "group2"}]}, {"key": "requestType", "label": "请求类型", "type": "input", "placeholder": "请选择"}, {"key": "responseDataType", "label": "响应数据标签", "type": "select", "placeholder": "或", "options": [{"label": "标签1", "value": "label1"}, {"label": "标签2", "value": "label2"}]}], "tabs": [{"key": "high", "label": "高风险", "count": 34}, {"key": "medium", "label": "中风险", "count": 38}, {"key": "low", "label": "低风险", "count": 8}, {"key": "none", "label": "无风险", "count": 124}, {"key": "other", "label": "其他", "count": 8}, {"key": "uncategorized", "label": "取消分组", "count": 0}], "activeTab": "high", "toolbarActions": [{"key": "batch", "label": "批量操作", "type": "default"}, {"key": "create", "label": "新建", "type": "primary"}], "rowActions": [{"key": "view", "label": "查看", "type": "default", "icon": "👁"}, {"key": "more", "label": "更多", "type": "default", "icon": "⋯"}], "pagination": {"current": 1, "pageSize": 25, "total": "{{api.getApiList.total}}", "showSizeChanger": true, "showQuickJumper": true, "showTotal": true}, "rowSelection": {"type": "checkbox"}}, "events": {"onSearch": "{{(values) => api.searchApi(values)}}", "onTabChange": "{{(key) => api.filterByRisk(key)}}", "onTableChange": "{{(pagination, filters, sorter) => api.updateTable(pagination, filters, sorter)}}"}, "style": {}, "className": ""}], "apis": [{"id": "getApiList", "name": "获取API列表", "url": "/api/v1/apis", "method": "GET", "params": {"page": "{{pagination.current}}", "pageSize": "{{pagination.pageSize}}", "riskLevel": "{{activeTab}}"}, "dataPath": "data.list"}, {"id": "searchApi", "name": "搜索API", "url": "/api/v1/apis/search", "method": "POST", "dataPath": "data.list"}, {"id": "filterByRisk", "name": "按风险等级筛选", "url": "/api/v1/apis/filter", "method": "GET", "params": {"riskLevel": "{{riskLevel}}"}, "dataPath": "data.list"}], "theme": {"primaryColor": "#1677ff", "backgroundColor": "#f8fafc", "textColor": "#374151", "borderColor": "#e5e7eb", "borderRadius": 8, "fontSize": 14}, "layout": {"type": "admin", "header": true, "sidebar": true, "footer": false}}